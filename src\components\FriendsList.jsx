import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  UserPlus, 
  Video, 
  MessageCircle, 
  MoreVertical,
  UserMinus,
  UserCheck,
  Clock,
  X
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

const FriendsList = ({ onStartCall }) => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('friends');
  const [friends, setFriends] = useState([]);
  const [friendRequests, setFriendRequests] = useState([]);
  const [showAddFriend, setShowAddFriend] = useState(false);
  const [newFriendEmail, setNewFriendEmail] = useState('');

  // Mock data - in a real app, this would come from an API
  useEffect(() => {
    const mockFriends = [
      {
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=alice',
        isOnline: true,
        lastSeen: new Date().toISOString(),
        status: 'Available for calls'
      },
      {
        id: '2',
        name: 'Bob Smith',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=bob',
        isOnline: false,
        lastSeen: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        status: 'In a meeting'
      },
      {
        id: '3',
        name: 'Carol Davis',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=carol',
        isOnline: true,
        lastSeen: new Date().toISOString(),
        status: 'Available'
      },
      {
        id: '4',
        name: 'David Wilson',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=david',
        isOnline: false,
        lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        status: 'Busy'
      }
    ];

    const mockRequests = [
      {
        id: '5',
        name: 'Emma Brown',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=emma',
        requestedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: '6',
        name: 'Frank Miller',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=frank',
        requestedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
      }
    ];

    setFriends(mockFriends);
    setFriendRequests(mockRequests);
  }, []);

  const filteredFriends = friends.filter(friend =>
    friend.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    friend.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddFriend = async () => {
    if (!newFriendEmail.trim()) return;

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newRequest = {
        id: Date.now().toString(),
        name: newFriendEmail.split('@')[0],
        email: newFriendEmail,
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${newFriendEmail}`,
        requestedAt: new Date().toISOString()
      };

      setFriendRequests(prev => [newRequest, ...prev]);
      setNewFriendEmail('');
      setShowAddFriend(false);
      toast.success('Friend request sent!');
    } catch (error) {
      toast.error('Failed to send friend request');
    }
  };

  const handleAcceptRequest = (requestId) => {
    const request = friendRequests.find(req => req.id === requestId);
    if (request) {
      const newFriend = {
        ...request,
        isOnline: Math.random() > 0.5,
        lastSeen: new Date().toISOString(),
        status: 'Available'
      };
      
      setFriends(prev => [newFriend, ...prev]);
      setFriendRequests(prev => prev.filter(req => req.id !== requestId));
      toast.success(`${request.name} is now your friend!`);
    }
  };

  const handleRejectRequest = (requestId) => {
    setFriendRequests(prev => prev.filter(req => req.id !== requestId));
    toast.success('Friend request declined');
  };

  const handleRemoveFriend = (friendId) => {
    const friend = friends.find(f => f.id === friendId);
    setFriends(prev => prev.filter(f => f.id !== friendId));
    toast.success(`${friend.name} removed from friends`);
  };

  const formatLastSeen = (lastSeen) => {
    const now = new Date();
    const lastSeenDate = new Date(lastSeen);
    const diffInMinutes = Math.floor((now - lastSeenDate) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <div className="bg-white rounded-lg shadow-md h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Contacts</h2>
          <button
            onClick={() => setShowAddFriend(true)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
          >
            <UserPlus className="w-5 h-5" />
          </button>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search friends..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Tabs */}
        <div className="flex mt-4 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('friends')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'friends'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Friends ({friends.length})
          </button>
          <button
            onClick={() => setActiveTab('requests')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors relative ${
              activeTab === 'requests'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Requests ({friendRequests.length})
            {friendRequests.length > 0 && (
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
            )}
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        <AnimatePresence mode="wait">
          {activeTab === 'friends' ? (
            <motion.div
              key="friends"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="p-4 space-y-3"
            >
              {filteredFriends.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <UserPlus className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>No friends found</p>
                  <p className="text-sm">Add some friends to get started!</p>
                </div>
              ) : (
                filteredFriends.map((friend) => (
                  <motion.div
                    key={friend.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors group"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <img
                          src={friend.avatar}
                          alt={friend.name}
                          className="w-10 h-10 rounded-full"
                        />
                        <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                          friend.isOnline ? 'bg-green-500' : 'bg-gray-400'
                        }`}></div>
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{friend.name}</h3>
                        <p className="text-sm text-gray-500">
                          {friend.isOnline ? friend.status : `Last seen ${formatLastSeen(friend.lastSeen)}`}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        onClick={() => onStartCall && onStartCall(friend)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-full transition-colors"
                        disabled={!friend.isOnline}
                      >
                        <Video className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors">
                        <MessageCircle className="w-4 h-4" />
                      </button>
                      <div className="relative group/menu">
                        <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-full transition-colors">
                          <MoreVertical className="w-4 h-4" />
                        </button>
                        <div className="absolute right-0 top-full mt-1 bg-white border rounded-lg shadow-lg py-1 opacity-0 invisible group-hover/menu:opacity-100 group-hover/menu:visible transition-all z-10">
                          <button
                            onClick={() => handleRemoveFriend(friend.id)}
                            className="w-full px-4 py-2 text-left text-red-600 hover:bg-red-50 flex items-center gap-2"
                          >
                            <UserMinus className="w-4 h-4" />
                            Remove Friend
                          </button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </motion.div>
          ) : (
            <motion.div
              key="requests"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="p-4 space-y-3"
            >
              {friendRequests.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Clock className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>No pending requests</p>
                </div>
              ) : (
                friendRequests.map((request) => (
                  <motion.div
                    key={request.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center justify-between p-3 bg-blue-50 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <img
                        src={request.avatar}
                        alt={request.name}
                        className="w-10 h-10 rounded-full"
                      />
                      <div>
                        <h3 className="font-medium text-gray-900">{request.name}</h3>
                        <p className="text-sm text-gray-500">
                          Sent {formatLastSeen(request.requestedAt)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleAcceptRequest(request.id)}
                        className="p-2 text-green-600 hover:bg-green-100 rounded-full transition-colors"
                      >
                        <UserCheck className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleRejectRequest(request.id)}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-full transition-colors"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  </motion.div>
                ))
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Add Friend Modal */}
      <AnimatePresence>
        {showAddFriend && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowAddFriend(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-6 w-full max-w-md mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Add Friend</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={newFriendEmail}
                    onChange={(e) => setNewFriendEmail(e.target.value)}
                    placeholder="Enter friend's email"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div className="flex gap-3">
                  <button
                    onClick={handleAddFriend}
                    className="flex-1 btn btn-primary"
                  >
                    Send Request
                  </button>
                  <button
                    onClick={() => setShowAddFriend(false)}
                    className="flex-1 btn btn-secondary"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default FriendsList;
