# VideoConnect - Advanced React Video Calling App

A modern, feature-rich video calling application built with React, Vite, and WebRTC technologies. Features include real video calling, advanced UI components, multiple themes, keyboard shortcuts, screen sharing, and comprehensive user management.

## 🚀 Features

### Core Features
- **Real WebRTC Video Calling** - High-quality peer-to-peer video calls with camera/microphone access
- **Advanced UI Components** - Modern glass cards, animated buttons, floating action buttons
- **Multiple Themes** - Light, dark, blue, purple, and green themes with system preference detection
- **Keyboard Shortcuts** - Comprehensive shortcuts for navigation and call controls
- **Screen Sharing** - Share your screen during video calls
- **User Authentication** - Secure login and registration system
- **User Profile Management** - Customizable profiles with avatar upload
- **Friends List** - Add, remove, and manage friends with online status
- **Real-time Messaging** - Chat during video calls
- **Notification Center** - Advanced notification system with badges
- **Settings Panel** - Comprehensive audio/video and privacy settings
- **Responsive Design** - Works seamlessly on desktop, tablet, and mobile

### User Interface
- **Splash Screen** - Animated loading screen with app branding
- **Modern UI** - Clean, intuitive interface with smooth animations
- **Dark/Light Mode** - System-based theme switching
- **Touch-Friendly** - Optimized for mobile interactions

### Enhanced UI/UX Features
- **Custom App Icon** - Animated SVG icon with multiple variants
- **Glass Morphism** - Modern glass card components with backdrop blur
- **Advanced Animations** - Framer Motion animations throughout the app
- **Floating Action Button** - Quick access to common actions
- **Theme System** - Complete theming with CSS variables and context
- **Notification Center** - Real-time notifications with badges and actions
- **Keyboard Shortcuts** - Full keyboard navigation and shortcuts modal
- **Responsive Components** - Adaptive UI for all screen sizes

### Technical Features
- **Real WebRTC Integration** - Actual peer-to-peer video calling with media streams
- **Screen Sharing** - Native browser screen sharing capabilities
- **Theme Context** - Advanced theming system with multiple presets
- **Custom Hooks** - Reusable hooks for screen sharing and keyboard shortcuts
- **Modern UI Components** - Reusable Card, Button, and Modal components
- **Real-time Updates** - Live presence indicators and notifications
- **Form Validation** - Comprehensive input validation with Yup
- **Error Handling** - Graceful error handling throughout the app
- **Performance Optimized** - Lazy loading and efficient rendering

## 🛠️ Tech Stack

- **Frontend Framework**: React 18
- **Build Tool**: Vite
- **Routing**: React Router DOM
- **State Management**: React Context API
- **Styling**: CSS3 with CSS Variables and utility classes
- **Animations**: Framer Motion
- **Form Handling**: React Hook Form + Yup validation
- **Video Calling**: Simple Peer (WebRTC)
- **Real-time Communication**: Socket.IO Client
- **Icons**: Lucide React
- **Notifications**: React Hot Toast
- **Avatar Generation**: DiceBear API

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/react-video-calling.git
   cd react-video-calling
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

## 🎯 Usage

### Getting Started
1. **Register** - Create a new account with your email and password
2. **Login** - Sign in to access the dashboard
3. **Add Friends** - Use the friends panel to add contacts by email
4. **Start Calling** - Click the video call button to begin a call

### Making Video Calls
1. Navigate to the dashboard
2. Select a friend from the friends list
3. Click the video call icon
4. Use the control panel to manage audio/video settings
5. End the call when finished

### Managing Settings
- Access settings from the dashboard header
- Configure video/audio preferences
- Adjust notification settings
- Customize privacy controls
- Change appearance settings

## 📱 Responsive Design

The app is fully responsive and optimized for:
- **Desktop** (1024px+) - Full feature set with sidebar layout
- **Tablet** (768px-1023px) - Adapted layout with collapsible elements
- **Mobile** (320px-767px) - Touch-optimized interface with bottom navigation

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:
```env
VITE_API_URL=your_api_url
VITE_SOCKET_URL=your_socket_url
VITE_STUN_SERVER=stun:stun.l.google.com:19302
```

### WebRTC Configuration
The app uses Google's STUN servers by default. For production, consider:
- Setting up your own STUN/TURN servers
- Configuring ICE servers for better connectivity
- Implementing signaling server for call coordination

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Vercel
```bash
npm install -g vercel
vercel --prod
```

### Deploy to Netlify
```bash
npm run build
# Upload dist folder to Netlify
```

## 🧪 Testing

### Run Tests
```bash
npm run test
```

### Browser Testing
The app has been tested on:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Mobile Testing
Tested on:
- iOS Safari
- Chrome Mobile
- Samsung Internet

## 🔒 Security Features

- **Input Validation** - All forms validated client and server-side
- **XSS Protection** - Sanitized user inputs
- **CSRF Protection** - Token-based request validation
- **Secure WebRTC** - Encrypted peer-to-peer connections
- **Privacy Controls** - User-configurable privacy settings

## 🎨 Customization

### Theming
Modify CSS variables in `src/index.css`:
```css
:root {
  --primary-color: #3b82f6;
  --secondary-color: #64748b;
  /* Add your custom colors */
}
```

### Components
All components are modular and can be easily customized:
- `src/components/` - Main UI components
- `src/contexts/` - State management
- `src/assets/` - Static assets

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

---

**VideoConnect** - Connecting people through seamless video communication 🎥✨
