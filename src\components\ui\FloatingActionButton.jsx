import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { Plus, Video, MessageCircle, Users, Phone } from 'lucide-react';

const FloatingActionButton = ({ onVideoCall, onMessage, onAddFriend }) => {
  const [isOpen, setIsOpen] = useState(false);

  const actions = [
    {
      icon: Video,
      label: 'Start Video Call',
      color: 'var(--color-primary)',
      onClick: onVideoCall
    },
    {
      icon: MessageCircle,
      label: 'Send Message',
      color: 'var(--color-success)',
      onClick: onMessage
    },
    {
      icon: Users,
      label: 'Add Friend',
      color: 'var(--color-warning)',
      onClick: onAddFriend
    }
  ];

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Action Buttons */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex flex-col gap-3 mb-4"
          >
            {actions.map((action, index) => (
              <motion.button
                key={action.label}
                initial={{ scale: 0, y: 20 }}
                animate={{ scale: 1, y: 0 }}
                exit={{ scale: 0, y: 20 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => {
                  action.onClick?.();
                  setIsOpen(false);
                }}
                className="group relative flex items-center gap-3 p-3 rounded-full shadow-lg"
                style={{
                  backgroundColor: action.color,
                  color: 'white'
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <action.icon className="w-6 h-6" />
                
                {/* Tooltip */}
                <motion.div
                  initial={{ opacity: 0, x: 10 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="absolute right-full mr-3 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg whitespace-nowrap"
                >
                  {action.label}
                  <div className="absolute top-1/2 -right-1 w-2 h-2 bg-gray-800 rotate-45 transform -translate-y-1/2" />
                </motion.div>
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main FAB */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="w-16 h-16 rounded-full shadow-lg flex items-center justify-center"
        style={{
          backgroundColor: 'var(--color-primary)',
          color: 'white'
        }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        animate={{ rotate: isOpen ? 45 : 0 }}
        transition={{ duration: 0.2 }}
      >
        <Plus className="w-8 h-8" />
      </motion.button>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-20 -z-10"
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default FloatingActionButton;
