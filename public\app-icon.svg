<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3b82f6" />
      <stop offset="50%" stop-color="#8b5cf6" />
      <stop offset="100%" stop-color="#3b82f6" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Main Circle -->
  <circle cx="50" cy="50" r="45" fill="url(#gradient)" filter="url(#shadow)" />
  
  <!-- Video Camera Icon -->
  <g transform="translate(50, 50)">
    <!-- Camera Body -->
    <rect x="-18" y="-8" width="24" height="16" rx="3" fill="white" opacity="0.95" />
    
    <!-- Camera Lens -->
    <circle cx="-6" cy="0" r="6" fill="#3b82f6" opacity="0.8" />
    
    <!-- Lens Center -->
    <circle cx="-6" cy="0" r="3" fill="white" />
    
    <!-- Recording Light -->
    <circle cx="8" cy="-6" r="2" fill="#10b981" />
    
    <!-- Connection Waves -->
    <g transform="translate(12, 0)">
      <path d="M 0 -8 Q 6 -4 0 0 Q 6 4 0 8" stroke="white" stroke-width="2" fill="none" opacity="0.7" />
      <path d="M 3 -6 Q 8 -3 3 0 Q 8 3 3 6" stroke="white" stroke-width="1.5" fill="none" opacity="0.5" />
    </g>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="20" cy="25" r="3" fill="#10b981" opacity="0.6" />
  <circle cx="80" cy="75" r="2" fill="#8b5cf6" opacity="0.7" />
</svg>
