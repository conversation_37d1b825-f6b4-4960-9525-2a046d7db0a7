import { useEffect } from 'react';
import toast from 'react-hot-toast';

export const useKeyboardShortcuts = (shortcuts = {}) => {
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Don't trigger shortcuts when typing in inputs
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
      }

      const key = event.key.toLowerCase();
      const ctrl = event.ctrlKey || event.metaKey;
      const shift = event.shiftKey;
      const alt = event.altKey;

      // Create a key combination string
      let combination = '';
      if (ctrl) combination += 'ctrl+';
      if (shift) combination += 'shift+';
      if (alt) combination += 'alt+';
      combination += key;

      // Check if this combination exists in shortcuts
      if (shortcuts[combination]) {
        event.preventDefault();
        shortcuts[combination]();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);
};

// Default shortcuts for the app
export const useAppShortcuts = (navigate, actions = {}) => {
  const shortcuts = {
    // Navigation shortcuts
    'ctrl+1': () => {
      navigate('/');
      toast.success('Navigated to Dashboard');
    },
    'ctrl+2': () => {
      navigate('/profile');
      toast.success('Navigated to Profile');
    },
    'ctrl+3': () => {
      navigate('/settings');
      toast.success('Navigated to Settings');
    },
    
    // Call shortcuts
    'ctrl+shift+v': () => {
      navigate('/call');
      toast.success('Starting video call');
    },
    
    // Theme shortcuts
    'ctrl+shift+t': () => {
      actions.toggleTheme?.();
      toast.success('Theme toggled');
    },
    
    // Search shortcut
    'ctrl+k': () => {
      actions.openSearch?.();
      toast.success('Search opened');
    },
    
    // Help shortcut
    'ctrl+shift+h': () => {
      actions.showHelp?.();
      toast.success('Help opened');
    },
    
    // Escape to close modals
    'escape': () => {
      actions.closeModals?.();
    }
  };

  useKeyboardShortcuts(shortcuts);
};

// Video call specific shortcuts
export const useVideoCallShortcuts = (callActions = {}) => {
  const shortcuts = {
    // Toggle video
    'ctrl+shift+v': () => {
      callActions.toggleVideo?.();
    },
    
    // Toggle audio
    'ctrl+shift+a': () => {
      callActions.toggleAudio?.();
    },
    
    // End call
    'ctrl+shift+e': () => {
      callActions.endCall?.();
    },
    
    // Toggle screen share
    'ctrl+shift+s': () => {
      callActions.toggleScreenShare?.();
    },
    
    // Toggle chat
    'ctrl+shift+c': () => {
      callActions.toggleChat?.();
    },
    
    // Mute/unmute (space bar)
    ' ': () => {
      callActions.toggleAudio?.();
    }
  };

  useKeyboardShortcuts(shortcuts);
};
