import { createContext, useContext, useState, useRef, useEffect } from 'react';
import Peer from 'simple-peer';
import toast from 'react-hot-toast';

const CallContext = createContext();

export const useCall = () => {
  const context = useContext(CallContext);
  if (!context) {
    throw new Error('useCall must be used within a CallProvider');
  }
  return context;
};

export const CallProvider = ({ children }) => {
  const [stream, setStream] = useState(null);
  const [remoteStream, setRemoteStream] = useState(null);
  const [call, setCall] = useState({});
  const [callAccepted, setCallAccepted] = useState(false);
  const [callEnded, setCallEnded] = useState(false);
  const [name, setName] = useState('');
  const [me, setMe] = useState('');
  const [otherUser, setOtherUser] = useState('');
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isCallIncoming, setIsCallIncoming] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');

  const myVideo = useRef();
  const userVideo = useRef();
  const connectionRef = useRef();
  const localStreamRef = useRef();

  // Initialize media stream
  useEffect(() => {
    const initializeMedia = async () => {
      try {
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 30 }
          },
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        });

        setStream(mediaStream);
        localStreamRef.current = mediaStream;

        if (myVideo.current) {
          myVideo.current.srcObject = mediaStream;
        }

        // Generate a unique ID for this user
        setMe(Math.random().toString(36).substring(2, 15));

        toast.success('Camera and microphone ready');
      } catch (error) {
        console.error('Error accessing media devices:', error);
        toast.error('Could not access camera/microphone. Please check permissions.');
      }
    };

    initializeMedia();

    return () => {
      if (localStreamRef.current) {
        localStreamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const callUser = async (friendData) => {
    if (!stream) {
      toast.error('Camera not ready. Please wait...');
      return;
    }

    try {
      setIsConnecting(true);
      setConnectionStatus('connecting');
      setOtherUser(friendData.name);

      // Create peer connection as initiator
      const peer = new Peer({
        initiator: true,
        trickle: false,
        stream: stream,
        config: {
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
          ]
        }
      });

      peer.on('signal', (data) => {
        console.log('Sending signal data:', data);
        // In a real app, this would be sent through a signaling server
        // For demo purposes, we'll simulate a direct connection
        setCall({
          isReceivingCall: false,
          from: me,
          name: friendData.name,
          signal: data
        });

        // Simulate call acceptance after 2 seconds for demo
        setTimeout(() => {
          simulateCallAcceptance(peer, data);
        }, 2000);
      });

      peer.on('stream', (remoteStream) => {
        console.log('Received remote stream');
        setRemoteStream(remoteStream);
        if (userVideo.current) {
          userVideo.current.srcObject = remoteStream;
        }
        setCallAccepted(true);
        setConnectionStatus('connected');
        setIsConnecting(false);
        toast.success(`Connected to ${friendData.name}`);
      });

      peer.on('connect', () => {
        console.log('Peer connection established');
        setConnectionStatus('connected');
      });

      peer.on('error', (error) => {
        console.error('Peer connection error:', error);
        toast.error('Connection failed');
        setConnectionStatus('failed');
        setIsConnecting(false);
      });

      peer.on('close', () => {
        console.log('Peer connection closed');
        setConnectionStatus('disconnected');
        setCallEnded(true);
      });

      connectionRef.current = peer;

    } catch (error) {
      console.error('Error starting call:', error);
      toast.error('Failed to start call');
      setIsConnecting(false);
      setConnectionStatus('failed');
    }
  };

  // Simulate call acceptance for demo purposes
  const simulateCallAcceptance = (peer, originalSignal) => {
    // Create a responding peer
    const respondingPeer = new Peer({
      initiator: false,
      trickle: false,
      stream: stream,
      config: {
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' },
          { urls: 'stun:stun1.l.google.com:19302' }
        ]
      }
    });

    respondingPeer.on('signal', (responseData) => {
      // Send response back to original peer
      peer.signal(responseData);
    });

    respondingPeer.on('stream', (remoteStream) => {
      // This would be the remote user's stream
      console.log('Simulated remote stream');
    });

    // Signal the responding peer with the original signal
    respondingPeer.signal(originalSignal);
  };

  const answerCall = () => {
    if (!stream) {
      toast.error('Camera not ready');
      return;
    }

    try {
      setCallAccepted(true);
      setIsCallIncoming(false);
      setConnectionStatus('connecting');

      const peer = new Peer({
        initiator: false,
        trickle: false,
        stream: stream,
        config: {
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
          ]
        }
      });

      peer.on('signal', (data) => {
        console.log('Answering call with signal:', data);
        // In a real app, this would be sent back through signaling server
      });

      peer.on('stream', (remoteStream) => {
        console.log('Received remote stream in answer');
        setRemoteStream(remoteStream);
        if (userVideo.current) {
          userVideo.current.srcObject = remoteStream;
        }
        setConnectionStatus('connected');
        toast.success('Call connected');
      });

      peer.on('error', (error) => {
        console.error('Answer call error:', error);
        toast.error('Failed to answer call');
        setConnectionStatus('failed');
      });

      peer.signal(call.signal);
      connectionRef.current = peer;

    } catch (error) {
      console.error('Error answering call:', error);
      toast.error('Failed to answer call');
    }
  };

  const leaveCall = () => {
    try {
      setCallEnded(true);
      setCallAccepted(false);
      setIsCallIncoming(false);
      setConnectionStatus('disconnected');
      setIsConnecting(false);

      // Close peer connection
      if (connectionRef.current) {
        connectionRef.current.destroy();
        connectionRef.current = null;
      }

      // Clear remote stream
      if (userVideo.current) {
        userVideo.current.srcObject = null;
      }
      setRemoteStream(null);

      // Reset call state
      setCall({});
      setOtherUser('');

      toast.success('Call ended');

    } catch (error) {
      console.error('Error ending call:', error);
    }
  };

  const toggleVideo = () => {
    if (stream) {
      const videoTrack = stream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsVideoEnabled(videoTrack.enabled);

        if (videoTrack.enabled) {
          toast.success('Camera turned on');
        } else {
          toast.success('Camera turned off');
        }
      }
    }
  };

  const toggleAudio = () => {
    if (stream) {
      const audioTrack = stream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsAudioEnabled(audioTrack.enabled);

        if (audioTrack.enabled) {
          toast.success('Microphone turned on');
        } else {
          toast.success('Microphone muted');
        }
      }
    }
  };

  // Start a test call with yourself (for demo purposes)
  const startTestCall = async () => {
    if (!stream) {
      toast.error('Camera not ready');
      return;
    }

    try {
      setIsConnecting(true);
      setConnectionStatus('connecting');
      setOtherUser('Test User');

      // Simulate connecting to yourself for testing
      setTimeout(() => {
        setRemoteStream(stream); // Use same stream as remote for demo
        if (userVideo.current) {
          userVideo.current.srcObject = stream;
        }
        setCallAccepted(true);
        setConnectionStatus('connected');
        setIsConnecting(false);
        toast.success('Test call started - you can see yourself!');
      }, 1500);

    } catch (error) {
      console.error('Error starting test call:', error);
      toast.error('Failed to start test call');
      setIsConnecting(false);
    }
  };

  const value = {
    call,
    callAccepted,
    myVideo,
    userVideo,
    stream,
    remoteStream,
    name,
    setName,
    callEnded,
    me,
    callUser,
    leaveCall,
    answerCall,
    isVideoEnabled,
    isAudioEnabled,
    toggleVideo,
    toggleAudio,
    isCallIncoming,
    otherUser,
    isConnecting,
    connectionStatus,
    startTestCall
  };

  return (
    <CallContext.Provider value={value}>
      {children}
    </CallContext.Provider>
  );
};
