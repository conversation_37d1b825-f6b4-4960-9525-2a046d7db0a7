import { motion, AnimatePresence } from 'framer-motion';
import { X, Keyboard, Video, Mic, Monitor, MessageCircle } from 'lucide-react';

const ShortcutsModal = ({ isOpen, onClose }) => {
  const shortcutCategories = [
    {
      title: 'Navigation',
      icon: Keyboard,
      shortcuts: [
        { keys: ['Ctrl', '1'], description: 'Go to Dashboard' },
        { keys: ['Ctrl', '2'], description: 'Go to Profile' },
        { keys: ['Ctrl', '3'], description: 'Go to Settings' },
        { keys: ['Ctrl', 'K'], description: 'Open Search' },
        { keys: ['Ctrl', 'Shift', 'H'], description: 'Show Help' }
      ]
    },
    {
      title: 'Video Calls',
      icon: Video,
      shortcuts: [
        { keys: ['Ctrl', 'Shift', 'V'], description: 'Start/Toggle Video' },
        { keys: ['Ctrl', 'Shift', 'A'], description: 'Toggle Audio' },
        { keys: ['Ctrl', 'Shift', 'E'], description: 'End Call' },
        { keys: ['Space'], description: 'Quick Mute/Unmute' }
      ]
    },
    {
      title: 'Screen Sharing',
      icon: Monitor,
      shortcuts: [
        { keys: ['Ctrl', 'Shift', 'S'], description: 'Toggle Screen Share' },
        { keys: ['Ctrl', 'Shift', 'C'], description: 'Toggle Chat' }
      ]
    },
    {
      title: 'General',
      icon: MessageCircle,
      shortcuts: [
        { keys: ['Ctrl', 'Shift', 'T'], description: 'Toggle Theme' },
        { keys: ['Esc'], description: 'Close Modals' },
        { keys: ['Ctrl', '/'], description: 'Show Shortcuts' }
      ]
    }
  ];

  const KeyBadge = ({ keyName }) => (
    <span 
      className="inline-flex items-center px-2 py-1 rounded text-xs font-mono font-medium"
      style={{
        backgroundColor: 'var(--color-bgSecondary)',
        color: 'var(--color-textPrimary)',
        border: '1px solid var(--color-borderLight)'
      }}
    >
      {keyName}
    </span>
  );

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="w-full max-w-4xl max-h-[90vh] overflow-y-auto rounded-lg shadow-xl"
            style={{
              backgroundColor: 'var(--color-bgPrimary)',
              border: '1px solid var(--color-borderLight)'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div 
              className="flex items-center justify-between p-6 border-b"
              style={{ borderColor: 'var(--color-borderLight)' }}
            >
              <div className="flex items-center gap-3">
                <div 
                  className="w-10 h-10 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: 'var(--color-primary)20' }}
                >
                  <Keyboard className="w-5 h-5" style={{ color: 'var(--color-primary)' }} />
                </div>
                <div>
                  <h2 className="text-xl font-bold" style={{ color: 'var(--color-textPrimary)' }}>
                    Keyboard Shortcuts
                  </h2>
                  <p className="text-sm" style={{ color: 'var(--color-textSecondary)' }}>
                    Speed up your workflow with these shortcuts
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 rounded-full transition-colors"
                style={{ 
                  color: 'var(--color-textSecondary)',
                  ':hover': { backgroundColor: 'var(--color-bgSecondary)' }
                }}
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {shortcutCategories.map((category, index) => (
                  <motion.div
                    key={category.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="space-y-4"
                  >
                    {/* Category Header */}
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-8 h-8 rounded-lg flex items-center justify-center"
                        style={{ backgroundColor: 'var(--color-primary)20' }}
                      >
                        <category.icon className="w-4 h-4" style={{ color: 'var(--color-primary)' }} />
                      </div>
                      <h3 className="font-semibold" style={{ color: 'var(--color-textPrimary)' }}>
                        {category.title}
                      </h3>
                    </div>

                    {/* Shortcuts List */}
                    <div className="space-y-3">
                      {category.shortcuts.map((shortcut, shortcutIndex) => (
                        <div 
                          key={shortcutIndex}
                          className="flex items-center justify-between p-3 rounded-lg"
                          style={{ backgroundColor: 'var(--color-bgSecondary)' }}
                        >
                          <span 
                            className="text-sm"
                            style={{ color: 'var(--color-textPrimary)' }}
                          >
                            {shortcut.description}
                          </span>
                          <div className="flex items-center gap-1">
                            {shortcut.keys.map((key, keyIndex) => (
                              <div key={keyIndex} className="flex items-center">
                                <KeyBadge keyName={key} />
                                {keyIndex < shortcut.keys.length - 1 && (
                                  <span 
                                    className="mx-1 text-xs"
                                    style={{ color: 'var(--color-textTertiary)' }}
                                  >
                                    +
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Footer Note */}
              <div 
                className="mt-8 p-4 rounded-lg"
                style={{ backgroundColor: 'var(--color-bgSecondary)' }}
              >
                <p 
                  className="text-sm text-center"
                  style={{ color: 'var(--color-textSecondary)' }}
                >
                  💡 <strong>Tip:</strong> Most shortcuts work globally throughout the app. 
                  Some shortcuts are context-specific and only work in certain areas.
                </p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ShortcutsModal;
