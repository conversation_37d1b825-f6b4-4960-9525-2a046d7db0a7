import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bell, 
  X, 
  Video, 
  MessageCircle, 
  UserPlus, 
  Settings,
  Check,
  Clock
} from 'lucide-react';

const NotificationCenter = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: 'call',
      title: 'Missed Call',
      message: '<PERSON> tried to call you',
      time: '2 minutes ago',
      unread: true,
      icon: Video,
      color: 'var(--color-primary)'
    },
    {
      id: 2,
      type: 'message',
      title: 'New Message',
      message: '<PERSON>: Hey, are you available for a quick call?',
      time: '5 minutes ago',
      unread: true,
      icon: MessageCircle,
      color: 'var(--color-success)'
    },
    {
      id: 3,
      type: 'friend',
      title: 'Friend Request',
      message: '<PERSON> wants to connect with you',
      time: '1 hour ago',
      unread: false,
      icon: UserPlus,
      color: 'var(--color-warning)'
    }
  ]);

  const unreadCount = notifications.filter(n => n.unread).length;

  const markAsRead = (id) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, unread: false } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, unread: false })));
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  return (
    <div className="relative">
      {/* Bell Icon */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 rounded-full transition-colors"
        style={{
          backgroundColor: isOpen ? 'var(--color-primary)' : 'var(--color-bgSecondary)',
          color: isOpen ? 'white' : 'var(--color-textSecondary)'
        }}
      >
        <Bell className="w-5 h-5" />
        
        {/* Unread Badge */}
        {unreadCount > 0 && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold text-white"
            style={{ backgroundColor: 'var(--color-error)' }}
          >
            {unreadCount > 9 ? '9+' : unreadCount}
          </motion.div>
        )}
      </motion.button>

      {/* Notification Panel */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Panel */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: -10 }}
              className="absolute right-0 top-full mt-2 w-80 z-50 rounded-lg shadow-lg border overflow-hidden"
              style={{
                backgroundColor: 'var(--color-bgPrimary)',
                borderColor: 'var(--color-borderLight)',
                boxShadow: 'var(--color-shadow)'
              }}
            >
              {/* Header */}
              <div 
                className="px-4 py-3 border-b flex items-center justify-between"
                style={{ borderColor: 'var(--color-borderLight)' }}
              >
                <h3 className="font-semibold" style={{ color: 'var(--color-textPrimary)' }}>
                  Notifications
                </h3>
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="text-sm font-medium"
                    style={{ color: 'var(--color-primary)' }}
                  >
                    Mark all read
                  </button>
                )}
              </div>

              {/* Notifications List */}
              <div className="max-h-96 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-8 text-center" style={{ color: 'var(--color-textSecondary)' }}>
                    <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No notifications</p>
                  </div>
                ) : (
                  <AnimatePresence>
                    {notifications.map((notification) => (
                      <motion.div
                        key={notification.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        className={`p-4 border-b cursor-pointer transition-colors ${
                          notification.unread ? 'bg-opacity-50' : ''
                        }`}
                        style={{ 
                          borderColor: 'var(--color-borderLight)',
                          backgroundColor: notification.unread ? 'var(--color-primary)10' : 'transparent'
                        }}
                        onClick={() => markAsRead(notification.id)}
                      >
                        <div className="flex items-start gap-3">
                          {/* Icon */}
                          <div 
                            className="w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0"
                            style={{ backgroundColor: `${notification.color}20` }}
                          >
                            <notification.icon 
                              className="w-5 h-5" 
                              style={{ color: notification.color }}
                            />
                          </div>

                          {/* Content */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h4 
                                className="font-medium text-sm"
                                style={{ color: 'var(--color-textPrimary)' }}
                              >
                                {notification.title}
                              </h4>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeNotification(notification.id);
                                }}
                                className="p-1 rounded-full hover:bg-gray-200 transition-colors"
                              >
                                <X className="w-3 h-3" style={{ color: 'var(--color-textTertiary)' }} />
                              </button>
                            </div>
                            <p 
                              className="text-sm mb-2 line-clamp-2"
                              style={{ color: 'var(--color-textSecondary)' }}
                            >
                              {notification.message}
                            </p>
                            <div className="flex items-center gap-2">
                              <Clock className="w-3 h-3" style={{ color: 'var(--color-textTertiary)' }} />
                              <span 
                                className="text-xs"
                                style={{ color: 'var(--color-textTertiary)' }}
                              >
                                {notification.time}
                              </span>
                              {notification.unread && (
                                <div 
                                  className="w-2 h-2 rounded-full ml-auto"
                                  style={{ backgroundColor: 'var(--color-primary)' }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                )}
              </div>

              {/* Footer */}
              {notifications.length > 0 && (
                <div 
                  className="px-4 py-3 border-t text-center"
                  style={{ borderColor: 'var(--color-borderLight)' }}
                >
                  <button 
                    className="text-sm font-medium"
                    style={{ color: 'var(--color-primary)' }}
                  >
                    View All Notifications
                  </button>
                </div>
              )}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default NotificationCenter;
