const AppIcon = ({ size = 48, className = "", variant = "default" }) => {
  const variants = {
    default: {
      primary: "#3b82f6",
      secondary: "#8b5cf6",
      accent: "#10b981"
    },
    dark: {
      primary: "#60a5fa",
      secondary: "#a78bfa",
      accent: "#34d399"
    },
    light: {
      primary: "#2563eb",
      secondary: "#7c3aed",
      accent: "#059669"
    }
  };

  const colors = variants[variant] || variants.default;

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Background Circle with Gradient */}
      <defs>
        <linearGradient id={`gradient-${variant}`} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={colors.primary} />
          <stop offset="50%" stopColor={colors.secondary} />
          <stop offset="100%" stopColor={colors.primary} />
        </linearGradient>
        <filter id={`shadow-${variant}`}>
          <feDropShadow dx="0" dy="4" stdDeviation="8" floodOpacity="0.3"/>
        </filter>
      </defs>
      
      {/* Main Circle */}
      <circle
        cx="50"
        cy="50"
        r="45"
        fill={`url(#gradient-${variant})`}
        filter={`url(#shadow-${variant})`}
      />
      
      {/* Video Camera Icon */}
      <g transform="translate(50, 50)">
        {/* Camera Body */}
        <rect
          x="-18"
          y="-8"
          width="24"
          height="16"
          rx="3"
          fill="white"
          opacity="0.95"
        />
        
        {/* Camera Lens */}
        <circle
          cx="-6"
          cy="0"
          r="6"
          fill={colors.primary}
          opacity="0.8"
        />
        
        {/* Lens Center */}
        <circle
          cx="-6"
          cy="0"
          r="3"
          fill="white"
        />
        
        {/* Recording Light */}
        <circle
          cx="8"
          cy="-6"
          r="2"
          fill={colors.accent}
        >
          <animate
            attributeName="opacity"
            values="0.3;1;0.3"
            dur="2s"
            repeatCount="indefinite"
          />
        </circle>
        
        {/* Connection Waves */}
        <g transform="translate(12, 0)">
          <path
            d="M 0 -8 Q 6 -4 0 0 Q 6 4 0 8"
            stroke="white"
            strokeWidth="2"
            fill="none"
            opacity="0.7"
          >
            <animate
              attributeName="opacity"
              values="0.3;0.8;0.3"
              dur="1.5s"
              repeatCount="indefinite"
            />
          </path>
          <path
            d="M 3 -6 Q 8 -3 3 0 Q 8 3 3 6"
            stroke="white"
            strokeWidth="1.5"
            fill="none"
            opacity="0.5"
          >
            <animate
              attributeName="opacity"
              values="0.2;0.6;0.2"
              dur="1.5s"
              begin="0.3s"
              repeatCount="indefinite"
            />
          </path>
        </g>
      </g>
      
      {/* Decorative Elements */}
      <circle cx="20" cy="25" r="3" fill={colors.accent} opacity="0.6">
        <animate
          attributeName="r"
          values="2;4;2"
          dur="3s"
          repeatCount="indefinite"
        />
      </circle>
      
      <circle cx="80" cy="75" r="2" fill={colors.secondary} opacity="0.7">
        <animate
          attributeName="r"
          values="1;3;1"
          dur="2.5s"
          begin="1s"
          repeatCount="indefinite"
        />
      </circle>
    </svg>
  );
};

export default AppIcon;
