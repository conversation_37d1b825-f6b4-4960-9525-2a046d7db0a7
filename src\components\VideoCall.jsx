import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Video,
  VideoOff,
  Mic,
  MicOff,
  Phone,
  PhoneOff,
  Monitor,
  Settings,
  MessageCircle,
  Users,
  Maximize,
  Minimize,
  Volume2,
  VolumeX
} from 'lucide-react';
import { useCall } from '../contexts/CallContext';
import { useNavigate, useLocation } from 'react-router-dom';
import CallInstructions from './CallInstructions';
import { useScreenShare } from '../hooks/useScreenShare';
import { useVideoCallShortcuts } from '../hooks/useKeyboardShortcuts';
import Button from './ui/Button';

const VideoCall = () => {
  const {
    myVideo,
    userVideo,
    stream,
    remoteStream,
    call,
    callAccepted,
    callEnded,
    leaveCall,
    answerCall,
    isVideoEnabled,
    isAudioEnabled,
    toggleVideo,
    toggleAudio,
    isCallIncoming,
    otherUser,
    isConnecting,
    connectionStatus,
    startTestCall,
    callUser
  } = useCall();

  const navigate = useNavigate();
  const location = useLocation();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [callDuration, setCallDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [chatMessages, setChatMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');

  // Screen sharing hook
  const { isScreenSharing, toggleScreenShare } = useScreenShare();

  const callStartTime = useRef(null);
  const controlsTimeout = useRef(null);

  const handleEndCall = () => {
    leaveCall();
    navigate('/');
  };

  // Keyboard shortcuts for video call
  useVideoCallShortcuts({
    toggleVideo,
    toggleAudio,
    endCall: handleEndCall,
    toggleScreenShare,
    toggleChat: () => setShowChat(!showChat)
  });

  // Auto-start call if friend data is passed from navigation
  useEffect(() => {
    const friendData = location.state?.friend;
    if (friendData && !callAccepted && !isConnecting && !callEnded) {
      console.log('Auto-starting call with:', friendData);
      callUser(friendData);
    }
  }, [location.state, callUser, callAccepted, isConnecting, callEnded]);

  useEffect(() => {
    if (callAccepted && !callEnded) {
      callStartTime.current = Date.now();
      const interval = setInterval(() => {
        setCallDuration(Math.floor((Date.now() - callStartTime.current) / 1000));
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [callAccepted, callEnded]);

  useEffect(() => {
    const handleMouseMove = () => {
      setShowControls(true);
      clearTimeout(controlsTimeout.current);
      controlsTimeout.current = setTimeout(() => {
        if (callAccepted && !callEnded) {
          setShowControls(false);
        }
      }, 3000);
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      clearTimeout(controlsTimeout.current);
    };
  }, [callAccepted, callEnded]);

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleScreenShare = async () => {
    try {
      await toggleScreenShare();
    } catch (error) {
      console.error('Error sharing screen:', error);
    }
  };

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const message = {
        id: Date.now(),
        text: newMessage,
        sender: 'me',
        timestamp: new Date().toLocaleTimeString()
      };
      setChatMessages(prev => [...prev, message]);
      setNewMessage('');
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  if (callEnded) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center text-white"
        >
          <div className="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <PhoneOff className="w-10 h-10" />
          </div>
          <h2 className="text-2xl font-bold mb-2">Call Ended</h2>
          <p className="text-gray-300 mb-6">
            Call duration: {formatDuration(callDuration)}
          </p>
          <button
            onClick={() => navigate('/')}
            className="btn btn-primary"
          >
            Back to Dashboard
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 relative overflow-hidden">
      {/* Incoming Call Modal */}
      <AnimatePresence>
        {isCallIncoming && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-8 text-center max-w-md mx-4"
            >
              <div className="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Video className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-2">{call.name || 'Unknown'}</h3>
              <p className="text-gray-600 mb-6">Incoming video call...</p>
              <div className="flex gap-4">
                <button
                  onClick={answerCall}
                  className="flex-1 bg-green-500 text-white py-3 px-6 rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center gap-2"
                >
                  <Phone className="w-5 h-5" />
                  Answer
                </button>
                <button
                  onClick={handleEndCall}
                  className="flex-1 bg-red-500 text-white py-3 px-6 rounded-lg hover:bg-red-600 transition-colors flex items-center justify-center gap-2"
                >
                  <PhoneOff className="w-5 h-5" />
                  Decline
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Video Streams */}
      <div className="relative w-full h-full">
        {/* Remote Video (Main) */}
        {callAccepted && remoteStream ? (
          <video
            ref={userVideo}
            autoPlay
            playsInline
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gray-800 flex items-center justify-center">
            <div className="text-center text-white">
              {isConnecting ? (
                <>
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                  <p>Connecting to {otherUser}...</p>
                </>
              ) : connectionStatus === 'failed' ? (
                <>
                  <VideoOff className="w-16 h-16 mx-auto mb-4 text-red-500" />
                  <p>Connection failed</p>
                </>
              ) : (
                <CallInstructions onStartTest={startTestCall} />
              )}
            </div>
          </div>
        )}

        {/* Local Video (Picture-in-Picture) */}
        <motion.div
          drag
          dragConstraints={{
            top: 20,
            left: 20,
            right: window.innerWidth - 220,
            bottom: window.innerHeight - 180
          }}
          className="absolute top-4 right-4 w-48 h-36 bg-gray-800 rounded-lg overflow-hidden shadow-lg cursor-move"
        >
          <video
            ref={myVideo}
            autoPlay
            playsInline
            muted
            className="w-full h-full object-cover"
          />
          {!isVideoEnabled && (
            <div className="absolute inset-0 bg-gray-800 flex items-center justify-center">
              <VideoOff className="w-8 h-8 text-gray-400" />
            </div>
          )}
        </motion.div>

        {/* Call Info */}
        <AnimatePresence>
          {showControls && callAccepted && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg"
            >
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">
                  {formatDuration(callDuration)}
                </span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Controls */}
        <AnimatePresence>
          {showControls && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              className="absolute bottom-6 left-1/2 transform -translate-x-1/2"
            >
              <div className="bg-black bg-opacity-75 backdrop-blur-sm rounded-full px-6 py-4 flex items-center gap-4">
                {/* Microphone */}
                <button
                  onClick={toggleAudio}
                  className={`p-3 rounded-full transition-colors ${
                    isAudioEnabled
                      ? 'bg-gray-700 hover:bg-gray-600 text-white'
                      : 'bg-red-500 hover:bg-red-600 text-white'
                  }`}
                >
                  {isAudioEnabled ? <Mic className="w-5 h-5" /> : <MicOff className="w-5 h-5" />}
                </button>

                {/* Camera */}
                <button
                  onClick={toggleVideo}
                  className={`p-3 rounded-full transition-colors ${
                    isVideoEnabled
                      ? 'bg-gray-700 hover:bg-gray-600 text-white'
                      : 'bg-red-500 hover:bg-red-600 text-white'
                  }`}
                >
                  {isVideoEnabled ? <Video className="w-5 h-5" /> : <VideoOff className="w-5 h-5" />}
                </button>

                {/* Screen Share */}
                <button
                  onClick={handleScreenShare}
                  className={`p-3 rounded-full transition-colors ${
                    isScreenSharing
                      ? 'bg-blue-500 hover:bg-blue-600 text-white'
                      : 'bg-gray-700 hover:bg-gray-600 text-white'
                  }`}
                >
                  <Monitor className="w-5 h-5" />
                </button>

                {/* Chat */}
                <button
                  onClick={() => setShowChat(!showChat)}
                  className="p-3 rounded-full bg-gray-700 hover:bg-gray-600 text-white transition-colors"
                >
                  <MessageCircle className="w-5 h-5" />
                </button>

                {/* Volume */}
                <button
                  onClick={() => setIsMuted(!isMuted)}
                  className={`p-3 rounded-full transition-colors ${
                    isMuted
                      ? 'bg-red-500 hover:bg-red-600 text-white'
                      : 'bg-gray-700 hover:bg-gray-600 text-white'
                  }`}
                >
                  {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                </button>

                {/* Fullscreen */}
                <button
                  onClick={toggleFullscreen}
                  className="p-3 rounded-full bg-gray-700 hover:bg-gray-600 text-white transition-colors"
                >
                  {isFullscreen ? <Minimize className="w-5 h-5" /> : <Maximize className="w-5 h-5" />}
                </button>

                {/* End Call */}
                <button
                  onClick={handleEndCall}
                  className="p-3 rounded-full bg-red-500 hover:bg-red-600 text-white transition-colors"
                >
                  <PhoneOff className="w-5 h-5" />
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Chat Panel */}
        <AnimatePresence>
          {showChat && (
            <motion.div
              initial={{ opacity: 0, x: 300 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 300 }}
              className="absolute right-4 top-4 bottom-20 w-80 bg-white rounded-lg shadow-lg flex flex-col"
            >
              <div className="p-4 border-b">
                <h3 className="font-semibold">Chat</h3>
              </div>
              <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {chatMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs px-3 py-2 rounded-lg ${
                        message.sender === 'me'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 text-gray-900'
                      }`}
                    >
                      <p className="text-sm">{message.text}</p>
                      <p className="text-xs opacity-75 mt-1">{message.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="p-4 border-t">
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="Type a message..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    onClick={handleSendMessage}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    Send
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default VideoCall;
