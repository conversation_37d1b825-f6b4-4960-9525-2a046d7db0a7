import { motion } from 'framer-motion';
import { Video, Users, MessageCircle, Settings } from 'lucide-react';
import Card from './Card';
import Button from './Button';

const ThemePreview = ({ theme, isActive, onClick }) => {
  const previewStyle = {
    '--color-primary': theme.colors.primary,
    '--color-primaryDark': theme.colors.primaryDark,
    '--color-secondary': theme.colors.secondary,
    '--color-success': theme.colors.success,
    '--color-error': theme.colors.error,
    '--color-warning': theme.colors.warning,
    '--color-bgPrimary': theme.colors.bgPrimary,
    '--color-bgSecondary': theme.colors.bgSecondary,
    '--color-bgTertiary': theme.colors.bgTertiary,
    '--color-textPrimary': theme.colors.textPrimary,
    '--color-textSecondary': theme.colors.textSecondary,
    '--color-textTertiary': theme.colors.textTertiary,
    '--color-borderLight': theme.colors.borderLight,
    '--color-borderMedium': theme.colors.borderMedium,
    '--color-shadow': theme.colors.shadow,
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      className={`relative p-4 rounded-lg cursor-pointer transition-all ${
        isActive ? 'ring-2 ring-offset-2' : ''
      }`}
      style={{
        ...previewStyle,
        backgroundColor: 'var(--color-bgSecondary)',
        border: `2px solid ${isActive ? 'var(--color-primary)' : 'var(--color-borderLight)'}`,
        ringColor: isActive ? 'var(--color-primary)' : 'transparent'
      }}
    >
      {/* Theme Name */}
      <div className="text-center mb-3">
        <h3 
          className="font-semibold text-sm"
          style={{ color: 'var(--color-textPrimary)' }}
        >
          {theme.name}
        </h3>
      </div>

      {/* Mini UI Preview */}
      <div className="space-y-2">
        {/* Header */}
        <div 
          className="h-6 rounded flex items-center px-2 gap-1"
          style={{ backgroundColor: 'var(--color-bgPrimary)' }}
        >
          <div 
            className="w-3 h-3 rounded-full flex items-center justify-center"
            style={{ backgroundColor: 'var(--color-primary)' }}
          >
            <Video className="w-1.5 h-1.5 text-white" />
          </div>
          <div 
            className="h-1 flex-1 rounded"
            style={{ backgroundColor: 'var(--color-textPrimary)' }}
          />
        </div>

        {/* Content Cards */}
        <div className="grid grid-cols-2 gap-1">
          <div 
            className="h-8 rounded p-1"
            style={{ backgroundColor: 'var(--color-bgPrimary)' }}
          >
            <div 
              className="h-2 rounded mb-1"
              style={{ backgroundColor: 'var(--color-primary)' }}
            />
            <div 
              className="h-1 rounded"
              style={{ backgroundColor: 'var(--color-textSecondary)' }}
            />
          </div>
          <div 
            className="h-8 rounded p-1"
            style={{ backgroundColor: 'var(--color-bgPrimary)' }}
          >
            <div 
              className="h-2 rounded mb-1"
              style={{ backgroundColor: 'var(--color-success)' }}
            />
            <div 
              className="h-1 rounded"
              style={{ backgroundColor: 'var(--color-textSecondary)' }}
            />
          </div>
        </div>

        {/* Button */}
        <div 
          className="h-4 rounded flex items-center justify-center"
          style={{ backgroundColor: 'var(--color-primary)' }}
        >
          <div className="w-2 h-2 bg-white rounded-full" />
        </div>
      </div>

      {/* Active Indicator */}
      {isActive && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center text-white text-xs"
          style={{ backgroundColor: 'var(--color-success)' }}
        >
          ✓
        </motion.div>
      )}
    </motion.div>
  );
};

export default ThemePreview;
