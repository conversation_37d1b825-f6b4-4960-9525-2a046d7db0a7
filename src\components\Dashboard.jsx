
import { 
  Video, 
  Users, 
  Settings, 
  User, 
  LogOut,
  Phone,
  MessageCircle,
  Calendar,
  Clock,
  TrendingUp
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import FriendsList from './FriendsList';
import AppIcon from './icons/AppIcon';
import ThemeSwitcher from './ThemeSwitcher';
import { useTheme } from '../contexts/ThemeContext';
import Card from './ui/Card';
import Button from './ui/Button';
import FloatingActionButton from './ui/FloatingActionButton';
import NotificationCenter from './ui/NotificationCenter';
import ShortcutsModal from './ui/ShortcutsModal';
import { useAppShortcuts } from '../hooks/useKeyboardShortcuts';
import { useState } from 'react';

const Dashboard = () => {
  const { user, logout } = useAuth();
  const { isDark, toggleDarkMode } = useTheme();
  const navigate = useNavigate();
  const [showShortcuts, setShowShortcuts] = useState(false);

  // Keyboard shortcuts
  useAppShortcuts(navigate, {
    toggleTheme: toggleDarkMode,
    showHelp: () => setShowShortcuts(true),
    closeModals: () => setShowShortcuts(false)
  });

  const handleStartCall = (friend) => {
    console.log('Starting call with:', friend);
    // Navigate to call page first, then the CallContext will handle the actual call
    navigate('/call', { state: { friend } });
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const stats = [
    {
      title: 'Total Calls',
      value: '24',
      change: '+12%',
      icon: Phone,
      color: 'bg-blue-500'
    },
    {
      title: 'Call Duration',
      value: '2h 45m',
      change: '+8%',
      icon: Clock,
      color: 'bg-green-500'
    },
    {
      title: 'Friends',
      value: '18',
      change: '+3',
      icon: Users,
      color: 'bg-purple-500'
    },
    {
      title: 'Messages',
      value: '156',
      change: '+24',
      icon: MessageCircle,
      color: 'bg-orange-500'
    }
  ];

  const recentCalls = [
    {
      id: 1,
      name: 'Alice Johnson',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=alice',
      type: 'incoming',
      duration: '12:34',
      time: '2 hours ago',
      status: 'completed'
    },
    {
      id: 2,
      name: 'Bob Smith',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=bob',
      type: 'outgoing',
      duration: '5:21',
      time: '4 hours ago',
      status: 'completed'
    },
    {
      id: 3,
      name: 'Carol Davis',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=carol',
      type: 'missed',
      duration: '0:00',
      time: '1 day ago',
      status: 'missed'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header
        className="shadow-sm border-b"
        style={{
          backgroundColor: 'var(--color-bgPrimary)',
          borderColor: 'var(--color-borderLight)'
        }}
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <AppIcon size={40} variant={isDark ? 'dark' : 'light'} />
              <h1 className="text-2xl font-bold" style={{ color: 'var(--color-textPrimary)' }}>
                VideoConnect
              </h1>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <img
                  src={user?.avatar || `https://api.dicebear.com/7.x/avataaars/svg?seed=${user?.email}`}
                  alt="Profile"
                  className="w-8 h-8 rounded-full"
                />
                <span className="text-gray-700 font-medium">{user?.name}</span>
              </div>

              <div className="flex items-center gap-2">
                <NotificationCenter />
                <ThemeSwitcher showLabel={false} size="sm" />
                <button
                  onClick={() => navigate('/profile')}
                  className="p-2 rounded-full transition-colors"
                  style={{
                    color: 'var(--color-textSecondary)',
                    ':hover': { backgroundColor: 'var(--color-bgSecondary)' }
                  }}
                >
                  <User className="w-5 h-5" />
                </button>
                <button
                  onClick={() => navigate('/settings')}
                  className="p-2 rounded-full transition-colors"
                  style={{
                    color: 'var(--color-textSecondary)',
                    ':hover': { backgroundColor: 'var(--color-bgSecondary)' }
                  }}
                >
                  <Settings className="w-5 h-5" />
                </button>
                <button
                  onClick={handleLogout}
                  className="p-2 rounded-full transition-colors"
                  style={{
                    color: 'var(--color-textSecondary)',
                    ':hover': { backgroundColor: 'var(--color-bgSecondary)' }
                  }}
                >
                  <LogOut className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Welcome Section */}
            <Card
              gradient={true}
              className="p-6 text-white"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <h2 className="text-2xl font-bold mb-2">
                Welcome back, {user?.name?.split(' ')[0]}! 👋
              </h2>
              <p className="opacity-90 mb-4">
                Ready to connect with your friends and colleagues?
              </p>
              <Button
                variant="secondary"
                onClick={() => navigate('/call')}
                icon={<Video className="w-5 h-5" />}
                className="bg-white text-blue-600 hover:bg-blue-50"
              >
                Start New Call
              </Button>
            </Card>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat, index) => (
                <Card
                  key={stat.title}
                  className="p-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                      <stat.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex items-center gap-1 text-green-600 text-sm">
                      <TrendingUp className="w-4 h-4" />
                      {stat.change}
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold" style={{ color: 'var(--color-textPrimary)' }}>{stat.value}</h3>
                  <p className="text-sm" style={{ color: 'var(--color-textSecondary)' }}>{stat.title}</p>
                </Card>
              ))}
            </div>

            {/* Recent Calls */}
            <Card
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <div
                className="p-6 border-b"
                style={{ borderColor: 'var(--color-borderLight)' }}
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold" style={{ color: 'var(--color-textPrimary)' }}>Recent Calls</h3>
                  <button
                    className="text-sm font-medium"
                    style={{ color: 'var(--color-primary)' }}
                  >
                    View All
                  </button>
                </div>
              </div>
              <div className="divide-y">
                {recentCalls.map((call) => (
                  <div key={call.id} className="p-6 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <img
                          src={call.avatar}
                          alt={call.name}
                          className="w-10 h-10 rounded-full"
                        />
                        <div>
                          <h4 className="font-medium text-gray-900">{call.name}</h4>
                          <div className="flex items-center gap-2 text-sm text-gray-500">
                            <span className={`w-2 h-2 rounded-full ${
                              call.type === 'incoming' ? 'bg-green-500' :
                              call.type === 'outgoing' ? 'bg-blue-500' : 'bg-red-500'
                            }`}></span>
                            <span className="capitalize">{call.type}</span>
                            <span>•</span>
                            <span>{call.time}</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">{call.duration}</p>
                        <p className={`text-xs ${
                          call.status === 'completed' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {call.status}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Quick Actions */}
            <Card
              className="p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-textPrimary)' }}>Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => navigate('/call')}
                  className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Video className="w-6 h-6 text-blue-600" />
                  <div className="text-left">
                    <h4 className="font-medium text-gray-900">Start Call</h4>
                    <p className="text-sm text-gray-500">Begin a new video call</p>
                  </div>
                </button>
                <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <Calendar className="w-6 h-6 text-green-600" />
                  <div className="text-left">
                    <h4 className="font-medium text-gray-900">Schedule</h4>
                    <p className="text-sm text-gray-500">Schedule a meeting</p>
                  </div>
                </button>
                <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <MessageCircle className="w-6 h-6 text-purple-600" />
                  <div className="text-left">
                    <h4 className="font-medium text-gray-900">Messages</h4>
                    <p className="text-sm text-gray-500">Send a message</p>
                  </div>
                </button>
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Friends List */}
            <div className="h-96">
              <FriendsList onStartCall={handleStartCall} />
            </div>

            {/* Status Card */}
            <Card
              className="p-6"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6 }}
            >
              <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-textPrimary)' }}>Your Status</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: 'var(--color-success)' }}
                  ></div>
                  <span style={{ color: 'var(--color-textPrimary)' }}>Available</span>
                </div>
                <div className="text-sm" style={{ color: 'var(--color-textSecondary)' }}>
                  Last active: Just now
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full mt-4"
                >
                  Change Status
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* Floating Action Button */}
      <FloatingActionButton
        onVideoCall={() => navigate('/call')}
        onMessage={() => toast.success('Message feature coming soon!')}
        onAddFriend={() => toast.success('Add friend feature coming soon!')}
      />

      {/* Shortcuts Modal */}
      <ShortcutsModal
        isOpen={showShortcuts}
        onClose={() => setShowShortcuts(false)}
      />
    </div>
  );
};

export default Dashboard;
