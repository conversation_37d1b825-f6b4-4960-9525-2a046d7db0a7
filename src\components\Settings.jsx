import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft,
  Video,
  Mic,
  Bell,
  Shield,
  Palette,
  Globe,
  HelpCircle,
  Info,
  Monitor,
  Volume2,
  Camera,
  Smartphone,
  Moon,
  Sun,
  Eye,
  Lock
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

const Settings = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [settings, setSettings] = useState({
    // Video & Audio
    defaultCamera: 'front',
    defaultMicrophone: 'default',
    videoQuality: 'hd',
    audioQuality: 'high',
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true,
    
    // Notifications
    callNotifications: true,
    messageNotifications: true,
    friendRequestNotifications: true,
    soundEnabled: true,
    vibrationEnabled: true,
    
    // Privacy
    showOnlineStatus: true,
    allowDirectCalls: true,
    shareReadReceipts: true,
    dataCollection: false,
    
    // Appearance
    theme: 'system',
    language: 'en',
    fontSize: 'medium',
    
    // Advanced
    autoStartVideo: true,
    autoStartAudio: true,
    recordCalls: false,
    bandwidth: 'auto'
  });

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
    toast.success('Setting updated');
  };

  const settingSections = [
    {
      id: 'video-audio',
      title: 'Video & Audio',
      icon: Video,
      settings: [
        {
          key: 'defaultCamera',
          label: 'Default Camera',
          type: 'select',
          options: [
            { value: 'front', label: 'Front Camera' },
            { value: 'back', label: 'Back Camera' },
            { value: 'external', label: 'External Camera' }
          ]
        },
        {
          key: 'videoQuality',
          label: 'Video Quality',
          type: 'select',
          options: [
            { value: 'low', label: 'Low (360p)' },
            { value: 'medium', label: 'Medium (720p)' },
            { value: 'hd', label: 'HD (1080p)' },
            { value: 'uhd', label: '4K (2160p)' }
          ]
        },
        {
          key: 'echoCancellation',
          label: 'Echo Cancellation',
          type: 'toggle',
          description: 'Reduce echo during calls'
        },
        {
          key: 'noiseSuppression',
          label: 'Noise Suppression',
          type: 'toggle',
          description: 'Filter background noise'
        }
      ]
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: Bell,
      settings: [
        {
          key: 'callNotifications',
          label: 'Call Notifications',
          type: 'toggle',
          description: 'Get notified about incoming calls'
        },
        {
          key: 'messageNotifications',
          label: 'Message Notifications',
          type: 'toggle',
          description: 'Get notified about new messages'
        },
        {
          key: 'soundEnabled',
          label: 'Sound',
          type: 'toggle',
          description: 'Play notification sounds'
        }
      ]
    },
    {
      id: 'privacy',
      title: 'Privacy & Security',
      icon: Shield,
      settings: [
        {
          key: 'showOnlineStatus',
          label: 'Show Online Status',
          type: 'toggle',
          description: 'Let others see when you\'re online'
        },
        {
          key: 'allowDirectCalls',
          label: 'Allow Direct Calls',
          type: 'toggle',
          description: 'Allow friends to call you directly'
        },
        {
          key: 'dataCollection',
          label: 'Data Collection',
          type: 'toggle',
          description: 'Allow anonymous usage data collection'
        }
      ]
    },
    {
      id: 'appearance',
      title: 'Appearance',
      icon: Palette,
      settings: [
        {
          key: 'theme',
          label: 'Theme',
          type: 'select',
          options: [
            { value: 'light', label: 'Light' },
            { value: 'dark', label: 'Dark' },
            { value: 'system', label: 'System' }
          ]
        },
        {
          key: 'language',
          label: 'Language',
          type: 'select',
          options: [
            { value: 'en', label: 'English' },
            { value: 'es', label: 'Spanish' },
            { value: 'fr', label: 'French' },
            { value: 'de', label: 'German' }
          ]
        }
      ]
    }
  ];

  const renderSetting = (setting) => {
    switch (setting.type) {
      case 'toggle':
        return (
          <div className="flex items-center justify-between">
            <div>
              <label className="font-medium text-gray-900">{setting.label}</label>
              {setting.description && (
                <p className="text-sm text-gray-500">{setting.description}</p>
              )}
            </div>
            <button
              onClick={() => handleSettingChange(setting.key, !settings[setting.key])}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                settings[setting.key] ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  settings[setting.key] ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        );
      
      case 'select':
        return (
          <div>
            <label className="block font-medium text-gray-900 mb-2">{setting.label}</label>
            <select
              value={settings[setting.key]}
              onChange={(e) => handleSettingChange(setting.key, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {setting.options.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate('/')}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Settings Navigation */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-4 sticky top-8">
                <nav className="space-y-2">
                  {settingSections.map((section) => (
                    <a
                      key={section.id}
                      href={`#${section.id}`}
                      className="flex items-center gap-3 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      <section.icon className="w-5 h-5" />
                      <span className="font-medium">{section.title}</span>
                    </a>
                  ))}
                </nav>
              </div>
            </div>

            {/* Settings Content */}
            <div className="lg:col-span-3 space-y-8">
              {settingSections.map((section, sectionIndex) => (
                <motion.div
                  key={section.id}
                  id={section.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: sectionIndex * 0.1 }}
                  className="bg-white rounded-lg shadow-md"
                >
                  <div className="p-6 border-b">
                    <div className="flex items-center gap-3">
                      <section.icon className="w-6 h-6 text-blue-600" />
                      <h2 className="text-xl font-semibold text-gray-900">{section.title}</h2>
                    </div>
                  </div>
                  <div className="p-6 space-y-6">
                    {section.settings.map((setting) => (
                      <div key={setting.key}>
                        {renderSetting(setting)}
                      </div>
                    ))}
                  </div>
                </motion.div>
              ))}

              {/* About Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="bg-white rounded-lg shadow-md"
              >
                <div className="p-6 border-b">
                  <div className="flex items-center gap-3">
                    <Info className="w-6 h-6 text-blue-600" />
                    <h2 className="text-xl font-semibold text-gray-900">About</h2>
                  </div>
                </div>
                <div className="p-6 space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Version</span>
                    <span className="font-medium">1.0.0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Build</span>
                    <span className="font-medium">2024.01.15</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Platform</span>
                    <span className="font-medium">Web</span>
                  </div>
                  <div className="pt-4 border-t">
                    <button className="text-blue-600 hover:text-blue-700 font-medium">
                      Check for Updates
                    </button>
                  </div>
                </div>
              </motion.div>

              {/* Danger Zone */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="bg-white rounded-lg shadow-md border-red-200"
              >
                <div className="p-6 border-b border-red-200">
                  <h2 className="text-xl font-semibold text-red-600">Danger Zone</h2>
                </div>
                <div className="p-6 space-y-4">
                  <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg">
                    <div>
                      <h3 className="font-medium text-gray-900">Clear All Data</h3>
                      <p className="text-sm text-gray-500">Remove all your data from this device</p>
                    </div>
                    <button className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                      Clear Data
                    </button>
                  </div>
                  <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg">
                    <div>
                      <h3 className="font-medium text-gray-900">Delete Account</h3>
                      <p className="text-sm text-gray-500">Permanently delete your account and all data</p>
                    </div>
                    <button className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                      Delete Account
                    </button>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
