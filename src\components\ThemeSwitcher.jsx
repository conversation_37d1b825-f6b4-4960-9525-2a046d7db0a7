import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Palette, 
  Sun, 
  Moon, 
  <PERSON>, 
  Check,
  Droplets,
  Zap,
  Leaf
} from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

const ThemeSwitcher = ({ showLabel = true, size = 'md' }) => {
  const { 
    currentTheme, 
    themes, 
    useSystemTheme, 
    changeTheme, 
    toggleSystemTheme,
    isDark 
  } = useTheme();
  
  const [isOpen, setIsOpen] = useState(false);

  const sizes = {
    sm: { button: 'p-2', icon: 'w-4 h-4', text: 'text-sm' },
    md: { button: 'p-3', icon: 'w-5 h-5', text: 'text-base' },
    lg: { button: 'p-4', icon: 'w-6 h-6', text: 'text-lg' }
  };

  const currentSize = sizes[size] || sizes.md;

  const themeIcons = {
    light: Sun,
    dark: Moon,
    blue: Droplets,
    purple: Zap,
    green: Leaf
  };

  const getCurrentIcon = () => {
    if (useSystemTheme) return Monitor;
    return themeIcons[currentTheme] || Palette;
  };

  const CurrentIcon = getCurrentIcon();

  return (
    <div className="relative">
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsOpen(!isOpen)}
        className={`${currentSize.button} rounded-full transition-all duration-200 flex items-center gap-2`}
        style={{
          backgroundColor: isOpen ? 'var(--color-primary)' : 'var(--color-bgSecondary)',
          color: isOpen ? 'white' : 'var(--color-textPrimary)',
          border: `1px solid ${isOpen ? 'var(--color-primary)' : 'var(--color-borderLight)'}`
        }}
      >
        <CurrentIcon className={currentSize.icon} />
        {showLabel && (
          <span className={currentSize.text}>
            {useSystemTheme ? 'System' : themes[currentTheme]?.name || 'Theme'}
          </span>
        )}
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Theme Menu */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: -10 }}
              className="absolute right-0 top-full mt-2 z-50 min-w-64 rounded-lg shadow-lg border overflow-hidden"
              style={{
                backgroundColor: 'var(--color-bgPrimary)',
                borderColor: 'var(--color-borderLight)',
                boxShadow: 'var(--color-shadow)'
              }}
            >
              {/* Header */}
              <div 
                className="px-4 py-3 border-b"
                style={{ borderColor: 'var(--color-borderLight)' }}
              >
                <h3 className="font-semibold flex items-center gap-2" style={{ color: 'var(--color-textPrimary)' }}>
                  <Palette className="w-4 h-4" />
                  Choose Theme
                </h3>
              </div>

              {/* System Theme Option */}
              <motion.button
                whileHover={{ backgroundColor: 'var(--color-bgSecondary)' }}
                onClick={() => {
                  toggleSystemTheme();
                  setIsOpen(false);
                }}
                className="w-full px-4 py-3 flex items-center justify-between transition-colors"
                style={{ color: 'var(--color-textPrimary)' }}
              >
                <div className="flex items-center gap-3">
                  <Monitor className="w-5 h-5" style={{ color: 'var(--color-secondary)' }} />
                  <div className="text-left">
                    <div className="font-medium">System</div>
                    <div className="text-sm" style={{ color: 'var(--color-textSecondary)' }}>
                      Follow system preference
                    </div>
                  </div>
                </div>
                {useSystemTheme && (
                  <Check className="w-4 h-4" style={{ color: 'var(--color-success)' }} />
                )}
              </motion.button>

              {/* Divider */}
              <div className="border-t" style={{ borderColor: 'var(--color-borderLight)' }} />

              {/* Theme Options */}
              <div className="py-2">
                {Object.entries(themes).map(([key, theme]) => {
                  const Icon = themeIcons[key] || Palette;
                  const isSelected = !useSystemTheme && currentTheme === key;
                  
                  return (
                    <motion.button
                      key={key}
                      whileHover={{ backgroundColor: 'var(--color-bgSecondary)' }}
                      onClick={() => {
                        changeTheme(key);
                        setIsOpen(false);
                      }}
                      className="w-full px-4 py-3 flex items-center justify-between transition-colors"
                      style={{ color: 'var(--color-textPrimary)' }}
                    >
                      <div className="flex items-center gap-3">
                        <Icon 
                          className="w-5 h-5" 
                          style={{ color: theme.colors.primary }} 
                        />
                        <div className="text-left">
                          <div className="font-medium">{theme.name}</div>
                          <div className="text-sm" style={{ color: 'var(--color-textSecondary)' }}>
                            {key === 'light' && 'Clean and bright'}
                            {key === 'dark' && 'Easy on the eyes'}
                            {key === 'blue' && 'Ocean inspired'}
                            {key === 'purple' && 'Creative and vibrant'}
                            {key === 'green' && 'Natural and calm'}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {/* Color Preview */}
                        <div className="flex gap-1">
                          <div 
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: theme.colors.primary }}
                          />
                          <div 
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: theme.colors.success }}
                          />
                        </div>
                        
                        {isSelected && (
                          <Check className="w-4 h-4" style={{ color: 'var(--color-success)' }} />
                        )}
                      </div>
                    </motion.button>
                  );
                })}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ThemeSwitcher;
