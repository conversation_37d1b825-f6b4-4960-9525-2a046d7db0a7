import { motion } from 'framer-motion';
import { forwardRef } from 'react';

const Card = forwardRef(({ 
  children, 
  className = '', 
  hover = true, 
  gradient = false,
  glowing = false,
  onClick,
  ...props 
}, ref) => {
  const baseStyles = {
    borderRadius: '1rem',
    border: '1px solid var(--color-borderLight)',
    backgroundColor: 'var(--color-bgPrimary)',
    boxShadow: 'var(--color-shadow)',
    transition: 'all 0.3s ease',
    overflow: 'hidden'
  };

  const hoverStyles = hover ? {
    whileHover: { 
      scale: 1.02,
      boxShadow: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04)'
    },
    whileTap: { scale: 0.98 }
  } : {};

  const gradientStyle = gradient ? {
    background: `linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%)`,
    color: 'white',
    border: 'none'
  } : {};

  const glowStyle = glowing ? {
    boxShadow: `0 0 20px var(--color-primary)40, var(--color-shadow)`
  } : {};

  return (
    <motion.div
      ref={ref}
      className={`${className}`}
      style={{
        ...baseStyles,
        ...gradientStyle,
        ...glowStyle
      }}
      onClick={onClick}
      {...hoverStyles}
      {...props}
    >
      {children}
    </motion.div>
  );
});

Card.displayName = 'Card';

export default Card;
