import { Video } from 'lucide-react';

const SimpleSplash = () => {
  console.log('SimpleSplash component is rendering');
  
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #3b82f6 0%, #9333ea 50%, #3730a3 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{ textAlign: 'center' }}>
        <div style={{
          width: '96px',
          height: '96px',
          margin: '0 auto 2rem',
          backgroundColor: 'white',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
        }}>
          <Video style={{ width: '48px', height: '48px', color: '#3b82f6' }} />
        </div>
        
        <h1 style={{
          fontSize: '3rem',
          fontWeight: 'bold',
          color: 'white',
          marginBottom: '1rem',
          margin: '0 0 1rem 0'
        }}>
          VideoConnect
        </h1>
        
        <p style={{
          fontSize: '1.25rem',
          color: '#dbeafe',
          marginBottom: '2rem',
          margin: '0 0 2rem 0'
        }}>
          Connect. Communicate. Collaborate.
        </p>
        
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '0.5rem',
          marginBottom: '2rem'
        }}>
          <div style={{
            width: '12px',
            height: '12px',
            backgroundColor: 'white',
            borderRadius: '50%',
            animation: 'pulse 1.5s infinite'
          }}></div>
          <div style={{
            width: '12px',
            height: '12px',
            backgroundColor: 'white',
            borderRadius: '50%',
            animation: 'pulse 1.5s infinite 0.2s'
          }}></div>
          <div style={{
            width: '12px',
            height: '12px',
            backgroundColor: 'white',
            borderRadius: '50%',
            animation: 'pulse 1.5s infinite 0.4s'
          }}></div>
          <span style={{
            color: 'white',
            marginLeft: '1rem'
          }}>
            Loading...
          </span>
        </div>
        
        <p style={{
          color: '#bfdbfe',
          fontSize: '0.875rem',
          margin: '0'
        }}>
          Version 1.0.0
        </p>
      </div>
      
      <style>{`
        @keyframes pulse {
          0%, 100% { opacity: 0.5; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.2); }
        }
      `}</style>
    </div>
  );
};

export default SimpleSplash;
