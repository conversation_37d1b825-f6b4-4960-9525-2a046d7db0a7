import { createContext, useContext, useState, useEffect } from 'react';
import toast from 'react-hot-toast';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

const themes = {
  light: {
    name: 'Light',
    colors: {
      primary: '#3b82f6',
      primaryDark: '#2563eb',
      secondary: '#64748b',
      success: '#10b981',
      error: '#ef4444',
      warning: '#f59e0b',
      bgPrimary: '#ffffff',
      bgSecondary: '#f8fafc',
      bgTertiary: '#f1f5f9',
      textPrimary: '#0f172a',
      textSecondary: '#475569',
      textTertiary: '#64748b',
      borderLight: '#e2e8f0',
      borderMedium: '#cbd5e1',
      shadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    }
  },
  dark: {
    name: 'Dark',
    colors: {
      primary: '#60a5fa',
      primaryDark: '#3b82f6',
      secondary: '#94a3b8',
      success: '#34d399',
      error: '#f87171',
      warning: '#fbbf24',
      bgPrimary: '#0f172a',
      bgSecondary: '#1e293b',
      bgTertiary: '#334155',
      textPrimary: '#f8fafc',
      textSecondary: '#cbd5e1',
      textTertiary: '#94a3b8',
      borderLight: '#475569',
      borderMedium: '#64748b',
      shadow: '0 4px 6px -1px rgb(0 0 0 / 0.3)',
    }
  },
  blue: {
    name: 'Ocean Blue',
    colors: {
      primary: '#0ea5e9',
      primaryDark: '#0284c7',
      secondary: '#64748b',
      success: '#06b6d4',
      error: '#ef4444',
      warning: '#f59e0b',
      bgPrimary: '#f0f9ff',
      bgSecondary: '#e0f2fe',
      bgTertiary: '#bae6fd',
      textPrimary: '#0c4a6e',
      textSecondary: '#075985',
      textTertiary: '#0369a1',
      borderLight: '#7dd3fc',
      borderMedium: '#38bdf8',
      shadow: '0 4px 6px -1px rgb(14 165 233 / 0.2)',
    }
  },
  purple: {
    name: 'Purple Dream',
    colors: {
      primary: '#a855f7',
      primaryDark: '#9333ea',
      secondary: '#64748b',
      success: '#10b981',
      error: '#ef4444',
      warning: '#f59e0b',
      bgPrimary: '#faf5ff',
      bgSecondary: '#f3e8ff',
      bgTertiary: '#e9d5ff',
      textPrimary: '#581c87',
      textSecondary: '#7c3aed',
      textTertiary: '#8b5cf6',
      borderLight: '#c4b5fd',
      borderMedium: '#a78bfa',
      shadow: '0 4px 6px -1px rgb(168 85 247 / 0.2)',
    }
  },
  green: {
    name: 'Nature Green',
    colors: {
      primary: '#059669',
      primaryDark: '#047857',
      secondary: '#64748b',
      success: '#10b981',
      error: '#ef4444',
      warning: '#f59e0b',
      bgPrimary: '#f0fdf4',
      bgSecondary: '#dcfce7',
      bgTertiary: '#bbf7d0',
      textPrimary: '#14532d',
      textSecondary: '#166534',
      textTertiary: '#15803d',
      borderLight: '#86efac',
      borderMedium: '#4ade80',
      shadow: '0 4px 6px -1px rgb(5 150 105 / 0.2)',
    }
  }
};

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState('light');
  const [systemPreference, setSystemPreference] = useState('light');
  const [useSystemTheme, setUseSystemTheme] = useState(false);

  // Detect system theme preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemPreference(mediaQuery.matches ? 'dark' : 'light');

    const handleChange = (e) => {
      setSystemPreference(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Load saved theme preference
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    const savedUseSystem = localStorage.getItem('useSystemTheme') === 'true';
    
    if (savedUseSystem) {
      setUseSystemTheme(true);
      setCurrentTheme(systemPreference);
    } else if (savedTheme && themes[savedTheme]) {
      setCurrentTheme(savedTheme);
    }
  }, [systemPreference]);

  // Apply theme to CSS variables
  useEffect(() => {
    const theme = themes[currentTheme];
    if (theme) {
      const root = document.documentElement;
      Object.entries(theme.colors).forEach(([key, value]) => {
        root.style.setProperty(`--color-${key}`, value);
      });
      
      // Update meta theme color
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute('content', theme.colors.primary);
      }
    }
  }, [currentTheme]);

  const changeTheme = (themeName) => {
    if (themes[themeName]) {
      setCurrentTheme(themeName);
      setUseSystemTheme(false);
      localStorage.setItem('theme', themeName);
      localStorage.setItem('useSystemTheme', 'false');
      toast.success(`Switched to ${themes[themeName].name} theme`);
    }
  };

  const toggleSystemTheme = () => {
    const newUseSystem = !useSystemTheme;
    setUseSystemTheme(newUseSystem);
    localStorage.setItem('useSystemTheme', newUseSystem.toString());
    
    if (newUseSystem) {
      setCurrentTheme(systemPreference);
      toast.success('Using system theme preference');
    } else {
      toast.success('Using manual theme selection');
    }
  };

  const toggleDarkMode = () => {
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    changeTheme(newTheme);
  };

  const value = {
    currentTheme,
    themes,
    systemPreference,
    useSystemTheme,
    changeTheme,
    toggleSystemTheme,
    toggleDarkMode,
    isDark: currentTheme === 'dark',
    themeColors: themes[currentTheme]?.colors || themes.light.colors
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
