import { motion } from 'framer-motion';

const GlassCard = ({ 
  children, 
  className = '', 
  blur = 'md',
  opacity = 0.8,
  border = true,
  hover = true,
  ...props 
}) => {
  const blurValues = {
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px'
  };

  const cardStyle = {
    background: `rgba(255, 255, 255, ${opacity})`,
    backdropFilter: `blur(${blurValues[blur] || blurValues.md})`,
    WebkitBackdropFilter: `blur(${blurValues[blur] || blurValues.md})`,
    border: border ? '1px solid rgba(255, 255, 255, 0.2)' : 'none',
    borderRadius: '1rem',
    boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
    transition: 'all 0.3s ease'
  };

  const hoverProps = hover ? {
    whileHover: { 
      scale: 1.02,
      boxShadow: '0 12px 40px 0 rgba(31, 38, 135, 0.5)'
    },
    whileTap: { scale: 0.98 }
  } : {};

  return (
    <motion.div
      className={`glass-card ${className}`}
      style={cardStyle}
      {...hoverProps}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export default GlassCard;
