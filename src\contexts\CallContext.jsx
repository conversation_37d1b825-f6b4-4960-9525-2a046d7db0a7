import { createContext, useContext, useState, useRef, useEffect } from 'react';
import Peer from 'simple-peer';
import { io } from 'socket.io-client';
import toast from 'react-hot-toast';

const CallContext = createContext();

export const useCall = () => {
  const context = useContext(CallContext);
  if (!context) {
    throw new Error('useCall must be used within a CallProvider');
  }
  return context;
};

export const CallProvider = ({ children }) => {
  const [stream, setStream] = useState(null);
  const [call, setCall] = useState({});
  const [callAccepted, setCallAccepted] = useState(false);
  const [callEnded, setCallEnded] = useState(false);
  const [name, setName] = useState('');
  const [me, setMe] = useState('');
  const [otherUser, setOtherUser] = useState('');
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isCallIncoming, setIsCallIncoming] = useState(false);

  const myVideo = useRef();
  const userVideo = useRef();
  const connectionRef = useRef();
  const socket = useRef();

  useEffect(() => {
    // Initialize socket connection (mock for now)
    socket.current = {
      on: (event, callback) => {
        // Mock socket events
        console.log(`Listening for ${event}`);
      },
      emit: (event, data) => {
        console.log(`Emitting ${event}:`, data);
      }
    };

    // Get user media
    navigator.mediaDevices.getUserMedia({ 
      video: true, 
      audio: true 
    })
    .then((currentStream) => {
      setStream(currentStream);
      if (myVideo.current) {
        myVideo.current.srcObject = currentStream;
      }
    })
    .catch((error) => {
      console.error('Error accessing media devices:', error);
      toast.error('Could not access camera/microphone');
    });

    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const callUser = (id) => {
    const peer = new Peer({ 
      initiator: true, 
      trickle: false, 
      stream 
    });

    peer.on('signal', (data) => {
      socket.current.emit('callUser', {
        userToCall: id,
        signalData: data,
        from: me,
        name
      });
    });

    peer.on('stream', (currentStream) => {
      if (userVideo.current) {
        userVideo.current.srcObject = currentStream;
      }
    });

    socket.current.on('callAccepted', (signal) => {
      setCallAccepted(true);
      peer.signal(signal);
    });

    connectionRef.current = peer;
  };

  const answerCall = () => {
    setCallAccepted(true);
    setIsCallIncoming(false);

    const peer = new Peer({ 
      initiator: false, 
      trickle: false, 
      stream 
    });

    peer.on('signal', (data) => {
      socket.current.emit('answerCall', { 
        signal: data, 
        to: call.from 
      });
    });

    peer.on('stream', (currentStream) => {
      if (userVideo.current) {
        userVideo.current.srcObject = currentStream;
      }
    });

    peer.signal(call.signal);
    connectionRef.current = peer;
  };

  const leaveCall = () => {
    setCallEnded(true);
    setCallAccepted(false);
    setIsCallIncoming(false);
    
    if (connectionRef.current) {
      connectionRef.current.destroy();
    }
    
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
    }
    
    // Get new stream for next call
    navigator.mediaDevices.getUserMedia({ 
      video: isVideoEnabled, 
      audio: isAudioEnabled 
    })
    .then((currentStream) => {
      setStream(currentStream);
      if (myVideo.current) {
        myVideo.current.srcObject = currentStream;
      }
    });

    window.location.reload();
  };

  const toggleVideo = () => {
    if (stream) {
      const videoTrack = stream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsVideoEnabled(videoTrack.enabled);
      }
    }
  };

  const toggleAudio = () => {
    if (stream) {
      const audioTrack = stream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsAudioEnabled(audioTrack.enabled);
      }
    }
  };

  const value = {
    call,
    callAccepted,
    myVideo,
    userVideo,
    stream,
    name,
    setName,
    callEnded,
    me,
    callUser,
    leaveCall,
    answerCall,
    isVideoEnabled,
    isAudioEnabled,
    toggleVideo,
    toggleAudio,
    isCallIncoming,
    otherUser
  };

  return (
    <CallContext.Provider value={value}>
      {children}
    </CallContext.Provider>
  );
};
