import { Video, Users, Monitor, Mic } from 'lucide-react';

const CallInstructions = ({ onStartTest }) => {
  return (
    <div style={{
      padding: '2rem',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderRadius: '1rem',
      color: 'white',
      maxWidth: '500px',
      margin: '0 auto'
    }}>
      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <Video style={{ width: '48px', height: '48px', margin: '0 auto 1rem', color: '#3b82f6' }} />
        <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
          Real WebRTC Video Calling
        </h2>
        <p style={{ color: '#cbd5e1' }}>
          This app now uses real WebRTC technology for video calls
        </p>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '1rem', color: '#3b82f6' }}>
          Features:
        </h3>
        <div style={{ display: 'grid', gap: '0.75rem' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <Video style={{ width: '20px', height: '20px', color: '#10b981' }} />
            <span>Real camera and microphone access</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <Users style={{ width: '20px', height: '20px', color: '#10b981' }} />
            <span>WebRTC peer-to-peer connections</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <Monitor style={{ width: '20px', height: '20px', color: '#10b981' }} />
            <span>Video/audio controls and settings</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <Mic style={{ width: '20px', height: '20px', color: '#10b981' }} />
            <span>Echo cancellation and noise suppression</span>
          </div>
        </div>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '1rem', color: '#f59e0b' }}>
          How to Test:
        </h3>
        <ol style={{ paddingLeft: '1.5rem', lineHeight: '1.6' }}>
          <li>Click "Start Test Call" to see yourself (demo mode)</li>
          <li>Use video/audio controls to toggle camera and microphone</li>
          <li>For real peer-to-peer calls, you need a signaling server</li>
          <li>The app is ready for production with a WebSocket server</li>
        </ol>
      </div>

      <div style={{ textAlign: 'center' }}>
        <button
          onClick={onStartTest}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            padding: '0.75rem 2rem',
            borderRadius: '0.5rem',
            border: 'none',
            fontSize: '1rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'background-color 0.2s'
          }}
          onMouseOver={(e) => e.target.style.backgroundColor = '#2563eb'}
          onMouseOut={(e) => e.target.style.backgroundColor = '#3b82f6'}
        >
          Start Test Call
        </button>
      </div>

      <div style={{ 
        marginTop: '1.5rem', 
        padding: '1rem', 
        backgroundColor: 'rgba(59, 130, 246, 0.1)', 
        borderRadius: '0.5rem',
        fontSize: '0.875rem',
        color: '#cbd5e1'
      }}>
        <strong>Note:</strong> This demo uses your local camera stream as both local and remote video. 
        In a real application, you would connect to other users through a signaling server.
      </div>
    </div>
  );
};

export default CallInstructions;
